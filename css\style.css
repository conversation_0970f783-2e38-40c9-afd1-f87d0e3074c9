/* iPhone 15 Pro 样式 */
.phone-frame {
    width: 393px;
    height: 852px;
    border: 8px solid #1f2937;
    border-radius: 40px;
    background: #000;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
    overflow: hidden;
}

.prototype-frame {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 2rem;
}

/* 通用样式 */
.app-container {
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow-x: hidden;
}

/* 状态栏 */
.status-bar {
    height: 44px;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    font-size: 14px;
    font-weight: 600;
}

/* 主要内容区域 */
.main-content {
    padding-bottom: 80px;
    min-height: calc(100vh - 124px);
}

/* 顶部导航 */
.top-nav {
    height: 60px;
    background: linear-gradient(90deg, #2d1b69 0%, #11998e 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.logo {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.user-status {
    font-size: 14px;
    color: #e2e8f0;
}

/* 底部导航 - 修改为纯文字样式 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(90deg, #1a1a2e 0%, #16213e 100%);
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.nav-item {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #94a3b8;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
}

.nav-item.active {
    color: #ffffff;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.4);
    transform: translateY(-2px);
}

.nav-item:hover {
    color: #e2e8f0;
    transform: translateY(-1px);
}

/* 移除图标样式 */
.nav-item i {
    display: none;
}

.nav-item span {
    font-size: 14px;
    font-weight: 500;
}

/* 菜单项样式 */
.menu-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.menu-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.menu-item i:first-child {
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

.menu-item span {
    flex: 1;
    font-size: 16px;
}

/* 分类标签样式 */
.category-tag {
    background: rgba(255, 255, 255, 0.1);
    color: #94a3b8;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    white-space: nowrap;
    transition: all 0.3s ease;
    cursor: pointer;
}

.category-tag.active {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.4);
}

/* 模特卡片样式 */
.model-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.model-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border-color: rgba(139, 92, 246, 0.5);
}

/* 内容区域 */
.content {
    padding: 20px;
    padding-bottom: 100px;
    min-height: calc(100vh - 184px);
}

/* 按钮样式 */
.btn-primary {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.btn-secondary {
    background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
    transition: all 0.3s ease;
}

/* 卡片样式 */
.card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* 输入框样式 */
.input-field {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    padding: 12px 16px;
    color: white;
    width: 100%;
    margin-bottom: 15px;
}

.input-field::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* 浮动按钮 */
.floating-btn {
    position: fixed;
    right: 20px;
    bottom: 120px;
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 4px 20px rgba(255, 107, 107, 0.4);
    z-index: 1000;
    transition: all 0.3s ease;
}

.floating-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(255, 107, 107, 0.6);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .phone-frame {
        width: 100%;
        max-width: 393px;
        height: 70vh;
        min-height: 600px;
    }
}