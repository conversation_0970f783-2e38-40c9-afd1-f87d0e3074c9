<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - APP原型预览</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .phone-frame {
            width: 300px;
            height: 650px; /* 增加高度从600px到650px */
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 35px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.4), 0 0 0 1px rgba(255,255,255,0.1);
            position: relative;
            margin: 0 auto;
            transition: all 0.3s ease;
        }
        
        .phone-frame:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.5), 0 0 0 1px rgba(255,255,255,0.2);
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            border-radius: 27px;
            overflow: hidden;
            background: #000;
            position: relative;
        }
        
        .phone-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 40px;
            padding: 40px 20px;
            max-width: 1600px;
            margin: 0 auto;
        }
        
        .phone-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 25px;
            text-align: center;
            transition: all 0.4s ease;
            border: 2px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .phone-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.6s;
        }
        
        .phone-item:hover::before {
            left: 100%;
        }
        
        .phone-item:hover {
            transform: translateY(-8px) scale(1.02);
            border-color: rgba(139, 92, 246, 0.5);
            box-shadow: 0 25px 50px rgba(139, 92, 246, 0.3);
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 700;
            color: white;
            margin-bottom: 12px;
            background: linear-gradient(45deg, #8b5cf6, #ec4899, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
        }
        
        .page-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .page-status {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        iframe {
            border: none;
            width: 100%;
            height: 100%;
            transform: scale(0.78);
            transform-origin: top left;
        }
        
        .iframe-container {
            width: 375px;
            height: 812px;
            overflow: hidden;
            margin: 0 auto;
            position: relative;
        }
        
        .phone-item .iframe-container {
            width: 284px;
            height: 634px; /* 增加高度从588px到634px */
        }
        
        .phone-item iframe {
            width: 375px;
            height: 812px;
            transform: scale(0.757); /* 调整缩放比例 */
        }
        
        .header-title {
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 3.5rem;
            font-weight: 800;
            text-align: center;
            margin-bottom: 2rem;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
        }
        
        .subtitle {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.2rem;
            margin-bottom: 3rem;
            font-weight: 300;
        }
        
        .stats-bar {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            border-radius: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #8b5cf6;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .phone-grid {
                grid-template-columns: 1fr;
                gap: 30px;
                padding: 20px 15px;
            }
            
            .phone-frame {
                width: 280px;
                height: 600px;
            }
            
            .phone-item .iframe-container {
                width: 264px;
                height: 584px;
            }
            
            .header-title {
                font-size: 2.5rem;
            }
            
            .stats-bar {
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container mx-auto py-8">
        <!-- 头部标题区域 -->
        <div class="text-center mb-12">
            <h1 class="header-title">
                时尚先锋摄影 APP
            </h1>
            <p class="subtitle">
                高保真原型预览 · 16个精美页面 · 完整用户体验
            </p>
            
            <!-- 统计信息 -->
            <div class="stats-bar">
                <div class="stat-item">
                    <div class="stat-number">16</div>
                    <div class="stat-label">页面总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">完成度</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">iPhone 15</div>
                    <div class="stat-label">适配尺寸</div>
                </div>
            </div>
        </div>
        
        <div class="phone-grid">
            <!-- 开屏页面 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">开屏页面</div>
                <div class="page-description">品牌启动画面 · 视觉冲击</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/splash.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 首页 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">首页</div>
                <div class="page-description">主要内容展示 · 导航中心</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/home.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 登录页面 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">登录页面</div>
                <div class="page-description">用户身份验证 · 安全登录</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/login.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 注册页面 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">注册页面</div>
                <div class="page-description">新用户注册 · 快速入门</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/register.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 找回密码 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">找回密码</div>
                <div class="page-description">密码重置 · 安全恢复</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/forgot-password.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- VIP会员 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">VIP会员</div>
                <div class="page-description">会员特权 · 尊享服务</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/vip.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 充值页面 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">充值页面</div>
                <div class="page-description">账户充值 · 便捷支付</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/recharge.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 社区页面 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">社区页面</div>
                <div class="page-description">用户交流 · 内容分享</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/community.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 帖子详情 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">帖子详情</div>
                <div class="page-description">内容详情 · 互动评论</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/post-detail.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 电子杂志详情 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">杂志详情</div>
                <div class="page-description">杂志介绍 · 购买预览</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/magazine-detail.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 电子杂志阅读 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">杂志阅读</div>
                <div class="page-description">在线阅读 · 沉浸体验</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/magazine-read.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索结果 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">搜索结果</div>
                <div class="page-description">智能搜索 · 精准匹配</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/search-results.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 排行榜 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">排行榜</div>
                <div class="page-description">热门排行 · 趋势追踪</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/ranking.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的页面 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">个人中心</div>
                <div class="page-description">个人资料 · 设置管理</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/profile.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模特页面 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">模特列表</div>
                <div class="page-description">模特展示 · 分类浏览</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/models.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模特详情页面 -->
            <div class="phone-item">
                <div class="page-status">Ready</div>
                <div class="page-title">模特详情</div>
                <div class="page-description">模特资料 · 作品展示</div>
                <div class="phone-frame">
                    <div class="phone-screen">
                        <div class="iframe-container">
                            <iframe src="pages/model-detail.html"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部信息 -->
        <div class="text-center mt-16 pb-8">
            <p class="text-white/60 text-sm">
                © 2024 时尚先锋摄影 APP · 高保真原型设计
            </p>
            <p class="text-white/40 text-xs mt-2">
                所有页面均已完成 · 支持响应式设计 · 优化用户体验
            </p>
        </div>
    </div>
</body>
</html>