<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充值</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <div class="app-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>9:41</span>
            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
        </div>
        
        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">JANS</div>
            <div class="user-status">
                <span class="text-sm">普通会员</span>
            </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content">
            <!-- 当前余额 -->
            <div class="card mb-6">
                <div class="text-center">
                    <h3 class="text-lg font-bold text-white mb-2">当前余额</h3>
                    <div class="text-4xl font-bold text-yellow-400 mb-2">¥0.00</div>
                    <button onclick="showRechargeInfo()" class="text-purple-400 text-sm">
                        <i class="fas fa-info-circle mr-1"></i>
                        充值说明
                    </button>
                </div>
            </div>
            
            <!-- 在线充值 -->
            <div class="card mb-6">
                <h4 class="text-lg font-bold text-white mb-4">
                    <i class="fas fa-globe mr-2 text-blue-400"></i>
                    在线充值
                </h4>
                <p class="text-gray-400 text-sm mb-4">通过第三方平台安全充值</p>
                <button onclick="window.open('https://www.123.com', '_blank')" class="w-full btn-primary">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    前往充值网站
                </button>
            </div>
            
            <!-- 充值卡充值 -->
            <div class="card mb-6">
                <h4 class="text-lg font-bold text-white mb-4">
                    <i class="fas fa-credit-card mr-2 text-green-400"></i>
                    充值卡充值
                </h4>
                <form class="space-y-4">
                    <div>
                        <label class="block text-white text-sm font-medium mb-2">卡号</label>
                        <input type="text" class="input-field" placeholder="请输入充值卡号">
                    </div>
                    <div>
                        <label class="block text-white text-sm font-medium mb-2">密码</label>
                        <input type="password" class="input-field" placeholder="请输入充值卡密码">
                    </div>
                    <button type="submit" onclick="rechargeCard(event)" class="w-full btn-secondary">
                        <i class="fas fa-plus mr-2"></i>
                        充值
                    </button>
                </form>
            </div>
            
            <!-- 充值记录 -->
            <button onclick="showRechargeHistory()" class="w-full p-4 bg-gray-800 rounded-xl text-white font-semibold border border-gray-600">
                <i class="fas fa-history mr-2"></i>
                充值记录
            </button>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="home.html" class="nav-item">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </a>
            <a href="models.html" class="nav-item">
                <i class="fas fa-user-friends"></i>
                <span>模特</span>
            </a>
            <a href="ranking.html" class="nav-item">
                <i class="fas fa-trophy"></i>
                <span>热榜</span>
            </a>
            <a href="profile.html" class="nav-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>
    
    <script>
        function showRechargeInfo() {
            alert('充值说明：\n\n1. 支持在线充值和充值卡充值\n2. 充值金额实时到账\n3. 充值记录可随时查询\n4. 如有问题请联系客服\n\n充值比例：1元=1积分');
        }
        
        function rechargeCard(event) {
            event.preventDefault();
            const cardNumber = event.target.form[0].value;
            const password = event.target.form[1].value;
            
            if (!cardNumber || !password) {
                alert('请输入完整的卡号和密码');
                return;
            }
            
            // 模拟充值结果
            if (Math.random() > 0.5) {
                alert('充值成功！已为您充值100元');
            } else {
                alert('充值失败！卡号或密码错误');
            }
        }
        
        function showRechargeHistory() {
            alert('充值记录：\n\n2024-01-15 14:30\n充值金额：¥100.00\n\n2024-01-10 09:15\n充值金额：¥50.00\n\n2024-01-05 16:45\n充值金额：¥200.00');
        }
    </script>
</body>
</html>