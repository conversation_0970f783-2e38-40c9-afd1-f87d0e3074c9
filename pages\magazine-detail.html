<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子杂志详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <div class="app-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>9:41</span>
            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
        </div>
        
        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">JANS</div>
            <div class="user-status">
                <span class="text-sm">普通会员</span>
            </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content">
            <!-- Banner推荐 -->
            <div class="relative mb-6 rounded-2xl overflow-hidden">
                <div class="h-40 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 flex items-center justify-center">
                    <div class="text-center text-white">
                        <h2 class="text-2xl font-bold mb-2">时尚前沿</h2>
                        <p class="text-sm opacity-90">本期精选推荐</p>
                    </div>
                </div>
            </div>
            
            <!-- VIP广告条 -->
            <div class="card mb-4 border-2 border-yellow-500">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-yellow-400 font-bold mb-1">升级VIP享受更多特权</h4>
                        <p class="text-gray-400 text-sm">无限阅读所有杂志内容</p>
                    </div>
                    <button onclick="window.location.href='vip.html'" class="btn-primary">
                        立即升级
                    </button>
                </div>
            </div>
            
            <!-- 购买规则 -->
            <button onclick="showPurchaseRules()" class="w-full mb-4 p-3 bg-blue-900 bg-opacity-30 border border-blue-500 border-opacity-50 rounded-lg text-blue-300 font-semibold">
                <i class="fas fa-info-circle mr-2"></i>
                本杂志购买规则
            </button>
            
            <!-- 搜索框 -->
            <div class="mb-6">
                <div class="relative">
                    <input type="text" class="input-field pr-12" placeholder="搜索期数、模特名">
                    <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-400">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            
            <!-- 杂志列表 -->
            <div class="space-y-4">
                <div class="card">
                    <div class="flex space-x-4">
                        <div class="w-24 h-32 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-image text-white text-2xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-white font-semibold mb-2">第156期 · 艾米丽 · 冬日暖阳</h3>
                            <p class="text-gray-400 text-sm mb-3">专业摄影师镜头下的冬日时尚大片</p>
                            <div class="flex items-center space-x-4 mb-3">
                                <button class="text-red-400 text-sm">
                                    <i class="fas fa-heart mr-1"></i>
                                    收藏
                                </button>
                                <button class="text-yellow-400 text-sm">
                                    <i class="fas fa-thumbs-up mr-1"></i>
                                    点赞
                                </button>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-purple-400 font-bold">¥15.00</span>
                                <button onclick="purchaseMagazine(156, 15)" class="btn-secondary text-sm px-4 py-1">
                                    购买
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="flex space-x-4">
                        <div class="w-24 h-32 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-image text-white text-2xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-white font-semibold mb-2">第155期 · 索菲亚 · 都市夜景</h3>
                            <p class="text-gray-400 text-sm mb-3">城市霓虹灯下的时尚魅力</p>
                            <div class="flex items-center space-x-4 mb-3">
                                <button class="text-red-400 text-sm">
                                    <i class="fas fa-heart mr-1"></i>
                                    收藏
                                </button>
                                <button class="text-yellow-400 text-sm">
                                    <i class="fas fa-thumbs-up mr-1"></i>
                                    点赞
                                </button>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-green-400 font-bold">已购买</span>
                                <button onclick="window.location.href='magazine-read.html'" class="bg-green-600 hover:bg-green-700 text-white text-sm px-4 py-1 rounded">
                                    阅读
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="flex space-x-4">
                        <div class="w-24 h-32 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-image text-white text-2xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-white font-semibold mb-2">第154期 · 维多利亚 · 复古风情</h3>
                            <p class="text-gray-400 text-sm mb-3">经典复古造型的时尚演绎</p>
                            <div class="flex items-center space-x-4 mb-3">
                                <button class="text-red-400 text-sm">
                                    <i class="fas fa-heart mr-1"></i>
                                    收藏
                                </button>
                                <button class="text-yellow-400 text-sm">
                                    <i class="fas fa-thumbs-up mr-1"></i>
                                    点赞
                                </button>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-purple-400 font-bold">¥18.00</span>
                                <button onclick="purchaseMagazine(154, 18)" class="btn-secondary text-sm px-4 py-1">
                                    购买
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分页 -->
            <div class="flex justify-center mt-6 space-x-2">
                <button class="w-8 h-8 bg-purple-600 text-white rounded flex items-center justify-center">
                    1
                </button>
                <button class="w-8 h-8 bg-gray-700 text-gray-300 rounded flex items-center justify-center">
                    2
                </button>
                <button class="w-8 h-8 bg-gray-700 text-gray-300 rounded flex items-center justify-center">
                    3
                </button>
                <button class="w-8 h-8 bg-gray-700 text-gray-300 rounded flex items-center justify-center">
                    <i class="fas fa-chevron-right text-xs"></i>
                </button>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="home.html" class="nav-item">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </a>
            <a href="models.html" class="nav-item">
                <i class="fas fa-user-friends"></i>
                <span>模特</span>
            </a>
            <a href="ranking.html" class="nav-item">
                <i class="fas fa-trophy"></i>
                <span>热榜</span>
            </a>
            <a href="profile.html" class="nav-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>
    
    <script>
        function showPurchaseRules() {
            alert('本杂志购买规则：\n\n1. 单期购买：¥15-20/期\n2. VIP会员免费阅读\n3. 购买后永久有效\n4. 支持高清下载\n5. 7天内可申请退款');
        }
        
        function purchaseMagazine(issue, price) {
            if (confirm(`确认购买第${issue}期杂志？\n价格：¥${price}.00`)) {
                alert('余额不足，请先充值！');
                if (confirm('是否前往充值页面？')) {
                    window.location.href = 'recharge.html';
                }
            }
        }
    </script>
</body>
</html>