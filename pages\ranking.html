<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>热榜</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <div class="app-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>9:41</span>
            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
        </div>
        
        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">JANS</div>
            <div class="user-status">
                <span class="text-sm">普通会员</span>
            </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content">
            <!-- 排行榜类型选择 -->
            <div class="mb-6">
                <div class="flex bg-gray-800 rounded-lg p-1">
                    <button onclick="showRanking('total')" id="totalTab" class="flex-1 py-2 px-3 rounded-md bg-purple-600 text-white font-semibold transition-all text-sm">
                        总榜
                    </button>
                    <button onclick="showRanking('fashion')" id="fashionTab" class="flex-1 py-2 px-3 rounded-md text-gray-400 font-semibold transition-all text-sm">
                        时尚前沿
                    </button>
                    <button onclick="showRanking('star')" id="starTab" class="flex-1 py-2 px-3 rounded-md text-gray-400 font-semibold transition-all text-sm">
                        明星写真
                    </button>
                </div>
            </div>
            
            <!-- 时间周期选择 -->
            <div class="mb-6">
                <div class="flex bg-gray-800 rounded-lg p-1">
                    <button onclick="showPeriod('total')" id="totalPeriod" class="flex-1 py-2 px-4 rounded-md bg-blue-600 text-white font-semibold transition-all">
                        总榜
                    </button>
                    <button onclick="showPeriod('month')" id="monthPeriod" class="flex-1 py-2 px-4 rounded-md text-gray-400 font-semibold transition-all">
                        月榜
                    </button>
                    <button onclick="showPeriod('quarter')" id="quarterPeriod" class="flex-1 py-2 px-4 rounded-md text-gray-400 font-semibold transition-all">
                        季度榜
                    </button>
                </div>
            </div>
            
            <!-- 排行榜内容 -->
            <div id="rankingContent">
                <!-- 前三名特殊显示 -->
                <div class="grid grid-cols-3 gap-4 mb-6">
                    <!-- 第二名 -->
                    <div class="text-center">
                        <div class="relative mb-2">
                            <div class="w-16 h-20 bg-gradient-to-br from-gray-400 to-gray-600 rounded-lg mx-auto flex items-center justify-center">
                                <i class="fas fa-image text-white"></i>
                            </div>
                            <div class="absolute -top-2 -right-2 w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                                2
                            </div>
                        </div>
                        <p class="text-white text-xs font-semibold">第154期</p>
                        <p class="text-gray-400 text-xs">1.8k</p>
                    </div>
                    
                    <!-- 第一名 -->
                    <div class="text-center">
                        <div class="relative mb-2">
                            <div class="w-20 h-24 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg mx-auto flex items-center justify-center">
                                <i class="fas fa-crown text-white text-xl"></i>
                            </div>
                            <div class="absolute -top-2 -right-2 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                                1
                            </div>
                        </div>
                        <p class="text-white text-xs font-semibold">第155期</p>
                        <p class="text-gray-400 text-xs">2.1k</p>
                    </div>
                    
                    <!-- 第三名 -->
                    <div class="text-center">
                        <div class="relative mb-2">
                            <div class="w-16 h-20 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg mx-auto flex items-center justify-center">
                                <i class="fas fa-image text-white"></i>
                            </div>
                            <div class="absolute -top-2 -right-2 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                                3
                            </div>
                        </div>
                        <p class="text-white text-xs font-semibold">第153期</p>
                        <p class="text-gray-400 text-xs">1.6k</p>
                    </div>
                </div>
                
                <!-- 4-10名列表 -->
                <div class="space-y-3">
                    <div class="card">
                        <div class="flex items-center space-x-4">
                            <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                4
                            </div>
                            <div class="w-12 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded flex items-center justify-center">
                                <i class="fas fa-image text-white text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-white font-semibold text-sm">第152期 · 艾米丽 · 冬日暖阳</p>
                                <p class="text-gray-400 text-xs">1.4k 浏览</p>
                            </div>
                            <div class="text-green-400 text-sm">
                                <i class="fas fa-arrow-up mr-1"></i>
                                +2
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="flex items-center space-x-4">
                            <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                5
                            </div>
                            <div class="w-12 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded flex items-center justify-center">
                                <i class="fas fa-image text-white text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-white font-semibold text-sm">第151期 · 维多利亚 · 复古风情</p>
                                <p class="text-gray-400 text-xs">1.3k 浏览</p>
                            </div>
                            <div class="text-gray-400 text-sm">
                                <i class="fas fa-minus mr-1"></i>
                                0
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="flex items-center space-x-4">
                            <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                6
                            </div>
                            <div class="w-12 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded flex items-center justify-center">
                                <i class="fas fa-image text-white text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-white font-semibold text-sm">第150期 · 安娜 · 都市丽人</p>
                                <p class="text-gray-400 text-xs">1.2k 浏览</p>
                            </div>
                            <div class="text-red-400 text-sm">
                                <i class="fas fa-arrow-down mr-1"></i>
                                -1
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="flex items-center space-x-4">
                            <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                7
                            </div>
                            <div class="w-12 h-16 bg-gradient-to-br from-red-500 to-pink-500 rounded flex items-center justify-center">
                                <i class="fas fa-image text-white text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-white font-semibold text-sm">第149期 · 莉莉 · 春日花语</p>
                                <p class="text-gray-400 text-xs">1.1k 浏览</p>
                            </div>
                            <div class="text-green-400 text-sm">
                                <i class="fas fa-arrow-up mr-1"></i>
                                +3
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="flex items-center space-x-4">
                            <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                8
                            </div>
                            <div class="w-12 h-16 bg-gradient-to-br from-yellow-500 to-orange-500 rounded flex items-center justify-center">
                                <i class="fas fa-image text-white text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-white font-semibold text-sm">第148期 · 凯特 · 夏日海滩</p>
                                <p class="text-gray-400 text-xs">1.0k 浏览</p>
                            </div>
                            <div class="text-gray-400 text-sm">
                                <i class="fas fa-minus mr-1"></i>
                                0
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="flex items-center space-x-4">
                            <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                9
                            </div>
                            <div class="w-12 h-16 bg-gradient-to-br from-indigo-500 to-purple-500 rounded flex items-center justify-center">
                                <i class="fas fa-image text-white text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-white font-semibold text-sm">第147期 · 娜塔莎 · 秋日私语</p>
                                <p class="text-gray-400 text-xs">950 浏览</p>
                            </div>
                            <div class="text-red-400 text-sm">
                                <i class="fas fa-arrow-down mr-1"></i>
                                -2
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="flex items-center space-x-4">
                            <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                10
                            </div>
                            <div class="w-12 h-16 bg-gradient-to-br from-pink-500 to-red-500 rounded flex items-center justify-center">
                                <i class="fas fa-image text-white text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-white font-semibold text-sm">第146期 · 伊莎贝拉 · 优雅晚装</p>
                                <p class="text-gray-400 text-xs">890 浏览</p>
                            </div>
                            <div class="text-green-400 text-sm">
                                <i class="fas fa-arrow-up mr-1"></i>
                                +1
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="home.html" class="nav-item">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </a>
            <a href="models.html" class="nav-item">
                <i class="fas fa-user-friends"></i>
                <span>模特</span>
            </a>
            <a href="ranking.html" class="nav-item active">
                <i class="fas fa-trophy"></i>
                <span>热榜</span>
            </a>
            <a href="profile.html" class="nav-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>
    
    <script>
        function showRanking(type) {
            // 重置所有标签
            document.getElementById('totalTab').className = 'flex-1 py-2 px-3 rounded-md text-gray-400 font-semibold transition-all text-sm';
            document.getElementById('fashionTab').className = 'flex-1 py-2 px-3 rounded-md text-gray-400 font-semibold transition-all text-sm';
            document.getElementById('starTab').className = 'flex-1 py-2 px-3 rounded-md text-gray-400 font-semibold transition-all text-sm';
            
            // 激活选中的标签
            document.getElementById(type + 'Tab').className = 'flex-1 py-2 px-3 rounded-md bg-purple-600 text-white font-semibold transition-all text-sm';
        }
        
        function showPeriod(period) {
            // 重置所有标签
            document.getElementById('totalPeriod').className = 'flex-1 py-2 px-4 rounded-md text-gray-400 font-semibold transition-all';
            document.getElementById('monthPeriod').className = 'flex-1 py-2 px-4 rounded-md text-gray-400 font-semibold transition-all';
            document.getElementById('quarterPeriod').className = 'flex-1 py-2 px-4 rounded-md text-gray-400 font-semibold transition-all';
            
            // 激活选中的标签
            document.getElementById(period + 'Period').className = 'flex-1 py-2 px-4 rounded-md bg-blue-600 text-white font-semibold transition-all';
        }
    </script>
</body>
</html>