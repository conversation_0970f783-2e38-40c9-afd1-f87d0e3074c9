<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模特详情 - 时尚先锋摄影</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../css/style.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 text-white min-h-screen">
    <!-- 状态栏 -->
    <div class="status-bar">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 头部导航 -->
        <div class="flex items-center justify-between p-4">
            <i class="fas fa-arrow-left text-xl" onclick="window.parent.showPage('models')"></i>
            <h1 class="text-lg font-bold">模特详情</h1>
            <div class="flex items-center space-x-3">
                <i class="fas fa-share-alt text-xl"></i>
                <i class="fas fa-ellipsis-v text-xl"></i>
            </div>
        </div>

        <!-- 模特头像和基本信息 -->
        <div class="relative">
            <div class="h-80 bg-gradient-to-b from-transparent to-black/50">
                <img src="https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=600&fit=crop&crop=face" alt="模特" class="w-full h-full object-cover">
            </div>
            <div class="absolute bottom-0 left-0 right-0 p-6">
                <div class="flex items-end justify-between">
                    <div>
                        <h2 class="text-2xl font-bold text-white mb-1">莉莉·詹姆斯</h2>
                        <p class="text-gray-200 mb-2">国际时装模特 · 英国伦敦</p>
                        <div class="flex items-center space-x-4 text-sm">
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 text-sm mr-2">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <span class="text-white">4.8</span>
                            </div>
                            <div class="text-gray-200">
                                <i class="fas fa-heart text-red-400 mr-1"></i>
                                12.5K
                            </div>
                        </div>
                    </div>
                    <button class="bg-gradient-to-r from-pink-500 to-purple-600 px-6 py-3 rounded-full font-medium">
                        <i class="fas fa-plus mr-2"></i>
                        关注
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计数据 -->
        <div class="grid grid-cols-3 gap-4 p-4">
            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                <div class="text-2xl font-bold text-pink-400">156</div>
                <div class="text-xs text-gray-300 mt-1">作品</div>
            </div>
            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                <div class="text-2xl font-bold text-purple-400">89K</div>
                <div class="text-xs text-gray-300 mt-1">粉丝</div>
            </div>
            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                <div class="text-2xl font-bold text-blue-400">234</div>
                <div class="text-xs text-gray-300 mt-1">关注</div>
            </div>
        </div>

        <!-- 个人简介 -->
        <div class="px-4 mb-6">
            <h3 class="text-lg font-bold mb-3">个人简介</h3>
            <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                <p class="text-gray-300 leading-relaxed">
                    国际知名时装模特，拥有10年专业经验。曾为多个国际知名品牌代言，包括Chanel、Dior、Versace等。擅长时装、内衣、泳装等多种风格拍摄，具有独特的镜头表现力和专业素养。
                </p>
                <div class="flex flex-wrap gap-2 mt-4">
                    <span class="bg-pink-500/20 text-pink-300 px-3 py-1 rounded-full text-xs">时装</span>
                    <span class="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full text-xs">内衣</span>
                    <span class="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-xs">泳装</span>
                    <span class="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-xs">商业</span>
                </div>
            </div>
        </div>

        <!-- 作品展示 -->
        <div class="px-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold">精选作品</h3>
                <span class="text-sm text-gray-400">查看全部</span>
            </div>
            <div class="grid grid-cols-2 gap-3">
                <div class="relative rounded-xl overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=200&h=300&fit=crop" alt="作品" class="w-full h-48 object-cover">
                    <div class="absolute top-2 right-2 bg-black/50 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full">
                        <i class="fas fa-heart text-red-400 mr-1"></i>
                        2.1K
                    </div>
                </div>
                <div class="relative rounded-xl overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=200&h=300&fit=crop" alt="作品" class="w-full h-48 object-cover">
                    <div class="absolute top-2 right-2 bg-black/50 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full">
                        <i class="fas fa-heart text-red-400 mr-1"></i>
                        1.8K
                    </div>
                </div>
                <div class="relative rounded-xl overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1509631179647-0177331693ae?w=200&h=300&fit=crop" alt="作品" class="w-full h-48 object-cover">
                    <div class="absolute top-2 right-2 bg-black/50 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full">
                        <i class="fas fa-heart text-red-400 mr-1"></i>
                        3.2K
                    </div>
                </div>
                <div class="relative rounded-xl overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=200&h=300&fit=crop" alt="作品" class="w-full h-48 object-cover">
                    <div class="absolute top-2 right-2 bg-black/50 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full">
                        <i class="fas fa-heart text-red-400 mr-1"></i>
                        2.7K
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bottom-nav">
        <div class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-fire"></i>
            <span>热榜</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-users"></i>
            <span>社区</span>
        </div>
        <div class="nav-item active">
            <i class="fas fa-female"></i>
            <span>模特</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>