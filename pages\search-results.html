<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索结果</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <div class="app-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>9:41</span>
            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
        </div>
        
        <!-- 顶部导航 -->
        <div class="top-nav">
            <button onclick="history.back()" class="text-white">
                <i class="fas fa-arrow-left"></i>
            </button>
            <div class="logo">搜索结果</div>
            <div></div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content">
            <!-- 搜索框 -->
            <div class="mb-6">
                <div class="relative">
                    <input type="text" class="input-field pr-12" placeholder="搜索期数、模特名" value="索菲亚">
                    <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-400">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            
            <!-- 搜索结果统计 -->
            <div class="mb-4">
                <p class="text-gray-400 text-sm">
                    找到 <span class="text-white font-semibold">8</span> 个相关结果
                </p>
            </div>
            
            <!-- 搜索结果列表 -->
            <div class="space-y-4">
                <div onclick="window.location.href='magazine-read.html'" class="card cursor-pointer hover:bg-opacity-20 transition-all">
                    <div class="flex space-x-4">
                        <div class="w-20 h-28 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-image text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-white font-semibold mb-2">第155期 · 索菲亚 · 都市夜景</h3>
                            <p class="text-gray-400 text-sm mb-2">城市霓虹灯下的时尚魅力</p>
                            <div class="flex items-center text-xs text-gray-500 mb-2">
                                <i class="fas fa-calendar mr-1"></i>
                                <span class="mr-3">2024-01-10</span>
                                <i class="fas fa-eye mr-1"></i>
                                <span>2.1k</span>
                            </div>
                            <span class="inline-block bg-green-600 text-white text-xs px-2 py-1 rounded">已购买</span>
                        </div>
                    </div>
                </div>
                
                <div onclick="window.location.href='magazine-detail.html'" class="card cursor-pointer hover:bg-opacity-20 transition-all">
                    <div class="flex space-x-4">
                        <div class="w-20 h-28 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-image text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-white font-semibold mb-2">第142期 · 索菲亚 · 春日花园</h3>
                            <p class="text-gray-400 text-sm mb-2">花园中的浪漫时尚写真</p>
                            <div class="flex items-center text-xs text-gray-500 mb-2">
                                <i class="fas fa-calendar mr-1"></i>
                                <span class="mr-3">2023-12-15</span>
                                <i class="fas fa-eye mr-1"></i>
                                <span>1.8k</span>
                            </div>
                            <span class="inline-block bg-purple-600 text-white text-xs px-2 py-1 rounded">¥18.00</span>
                        </div>
                    </div>
                </div>
                
                <div onclick="window.location.href='magazine-detail.html'" class="card cursor-pointer hover:bg-opacity-20 transition-all">
                    <div class="flex space-x-4">
                        <div class="w-20 h-28 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-image text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-white font-semibold mb-2">第138期 · 索菲亚 · 海边度假</h3>
                            <p class="text-gray-400 text-sm mb-2">海滩度假风情大片</p>
                            <div class="flex items-center text-xs text-gray-500 mb-2">
                                <i class="fas fa-calendar mr-1"></i>
                                <span class="mr-3">2023-11-20</span>
                                <i class="fas fa-eye mr-1"></i>
                                <span>2.5k</span>
                            </div>
                            <span class="inline-block bg-purple-600 text-white text-xs px-2 py-1 rounded">¥16.00</span>
                        </div>
                    </div>
                </div>
                
                <div onclick="window.location.href='magazine-detail.html'" class="card cursor-pointer hover:bg-opacity-20 transition-all">
                    <div class="flex space-x-4">
                        <div class="w-20 h-28 bg-gradient-to-br from-red-500 to-pink-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-image text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-white font-semibold mb-2">第125期 · 索菲亚 · 秋日私语</h3>
                            <p class="text-gray-400 text-sm mb-2">秋天的温柔时光</p>
                            <div class="flex items-center text-xs text-gray-500 mb-2">
                                <i class="fas fa-calendar mr-1"></i>
                                <span class="mr-3">2023-09-10</span>
                                <i class="fas fa-eye mr-1"></i>
                                <span>1.9k</span>
                            </div>
                            <span class="inline-block bg-purple-600 text-white text-xs px-2 py-1 rounded">¥15.00</span>
                        </div>
                    </div>
                </div>
                
                <div onclick="window.location.href='magazine-detail.html'" class="card cursor-pointer hover:bg-opacity-20 transition-all">
                    <div class="flex space-x-4">
                        <div class="w-20 h-28 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-image text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-white font-semibold mb-2">第118期 · 索菲亚 · 夏日清新</h3>
                            <p class="text-gray-400 text-sm mb-2">清新夏日风格写真</p>
                            <div class="flex items-center text-xs text-gray-500 mb-2">
                                <i class="fas fa-calendar mr-1"></i>
                                <span class="mr-3">2023-07-25</span>
                                <i class="fas fa-eye mr-1"></i>
                                <span>2.2k</span>
                            </div>
                            <span class="inline-block bg-purple-600 text-white text-xs px-2 py-1 rounded">¥17.00</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 加载更多 -->
            <div class="text-center mt-6">
                <button class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>
                    加载更多
                </button>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="home.html" class="nav-item">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </a>
            <a href="models.html" class="nav-item">
                <i class="fas fa-user-friends"></i>
                <span>模特</span>
            </a>
            <a href="ranking.html" class="nav-item">
                <i class="fas fa-trophy"></i>
                <span>热榜</span>
            </a>
            <a href="profile.html" class="nav-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>
</body>
</html>