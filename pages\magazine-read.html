<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>杂志阅读</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <div class="app-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>9:41</span>
            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
        </div>
        
        <!-- 顶部导航 -->
        <div class="top-nav">
            <button onclick="history.back()" class="text-white">
                <i class="fas fa-arrow-left"></i>
            </button>
            <div class="logo">第155期阅读</div>
            <div></div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content">
            <!-- 杂志封面 -->
            <div class="mb-6">
                <div class="w-full h-64 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 rounded-2xl flex items-center justify-center relative overflow-hidden">
                    <div class="absolute inset-0 bg-black bg-opacity-30"></div>
                    <div class="relative z-10 text-center text-white">
                        <h1 class="text-2xl font-bold mb-2">第155期</h1>
                        <p class="text-lg">索菲亚 · 都市夜景</p>
                        <div class="mt-4">
                            <i class="fas fa-camera text-4xl"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 选项卡 -->
            <div class="mb-6">
                <div class="flex bg-gray-800 rounded-lg p-1">
                    <button onclick="showTab('images')" id="imagesTab" class="flex-1 py-2 px-4 rounded-md bg-purple-600 text-white font-semibold transition-all">
                        <i class="fas fa-images mr-2"></i>
                        杂志图片
                    </button>
                    <button onclick="showTab('videos')" id="videosTab" class="flex-1 py-2 px-4 rounded-md text-gray-400 font-semibold transition-all">
                        <i class="fas fa-video mr-2"></i>
                        杂志视频
                    </button>
                </div>
            </div>
            
            <!-- 图片内容 -->
            <div id="imagesContent" class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div class="aspect-square bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-image text-white text-2xl"></i>
                    </div>
                    <div class="aspect-square bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-image text-white text-2xl"></i>
                    </div>
                    <div class="aspect-square bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-image text-white text-2xl"></i>
                    </div>
                    <div class="aspect-square bg-gradient-to-br from-red-500 to-pink-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-image text-white text-2xl"></i>
                    </div>
                    <div class="aspect-square bg-gradient-to-br from-yellow-500 to-red-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-image text-white text-2xl"></i>
                    </div>
                    <div class="aspect-square bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-image text-white text-2xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- 视频内容 -->
            <div id="videosContent" class="hidden space-y-4">
                <div class="card">
                    <div class="relative">
                        <div class="w-full h-48 bg-gradient-to-br from-purple-600 to-pink-600 rounded-lg flex items-center justify-center cursor-pointer" onclick="playVideo()">
                            <div class="text-center text-white">
                                <i class="fas fa-play-circle text-6xl mb-2"></i>
                                <p class="text-lg font-semibold">都市夜景拍摄花絮</p>
                                <p class="text-sm opacity-80">点击播放</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="relative">
                        <div class="w-full h-48 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center cursor-pointer" onclick="playVideo()">
                            <div class="text-center text-white">
                                <i class="fas fa-play-circle text-6xl mb-2"></i>
                                <p class="text-lg font-semibold">摄影师访谈</p>
                                <p class="text-sm opacity-80">点击播放</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 高清下载浮动按钮 -->
        <button onclick="downloadContent()" class="fixed right-4 bottom-32 w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center text-white shadow-lg">
            <i class="fas fa-download text-xl"></i>
        </button>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="home.html" class="nav-item">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </a>
            <a href="models.html" class="nav-item">
                <i class="fas fa-user-friends"></i>
                <span>模特</span>
            </a>
            <a href="ranking.html" class="nav-item">
                <i class="fas fa-trophy"></i>
                <span>热榜</span>
            </a>
            <a href="profile.html" class="nav-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>
    
    <script>
        function showTab(tab) {
            const imagesTab = document.getElementById('imagesTab');
            const videosTab = document.getElementById('videosTab');
            const imagesContent = document.getElementById('imagesContent');
            const videosContent = document.getElementById('videosContent');
            
            if (tab === 'images') {
                imagesTab.className = 'flex-1 py-2 px-4 rounded-md bg-purple-600 text-white font-semibold transition-all';
                videosTab.className = 'flex-1 py-2 px-4 rounded-md text-gray-400 font-semibold transition-all';
                imagesContent.classList.remove('hidden');
                videosContent.classList.add('hidden');
            } else {
                imagesTab.className = 'flex-1 py-2 px-4 rounded-md text-gray-400 font-semibold transition-all';
                videosTab.className = 'flex-1 py-2 px-4 rounded-md bg-purple-600 text-white font-semibold transition-all';
                imagesContent.classList.add('hidden');
                videosContent.classList.remove('hidden');
            }
        }
        
        function playVideo() {
            alert('您还不是VIP会员，请购买VIP会员或单独购买本期内容！\n\n单期价格：¥15.00');
            if (confirm('是否前往VIP购买页面？')) {
                window.location.href = 'vip.html';
            }
        }
        
        function downloadContent() {
            alert('您还不是VIP会员，请购买VIP会员后享受高清下载服务！\n\n下载说明：\n- VIP会员每日可下载10次\n- 超级VIP无限下载\n- 支持手机和电脑端下载');
            if (confirm('是否前往VIP购买页面？')) {
                window.location.href = 'vip.html';
            }
        }
    </script>
</body>
</html>