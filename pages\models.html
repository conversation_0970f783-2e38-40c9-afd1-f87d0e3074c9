<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模特 - 时尚先锋摄影</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../css/style.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 text-white min-h-screen">
    <!-- 状态栏 -->
    <div class="status-bar">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 头部 -->
        <div class="flex items-center justify-between p-4">
            <h1 class="text-2xl font-bold bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent">时尚模特</h1>
            <div class="flex items-center space-x-3">
                <i class="fas fa-search text-xl text-gray-300"></i>
                <i class="fas fa-filter text-xl text-gray-300"></i>
            </div>
        </div>

        <!-- 分类标签 -->
        <div class="flex space-x-3 px-4 mb-6 overflow-x-auto">
            <div class="category-tag active">全部</div>
            <div class="category-tag">时装</div>
            <div class="category-tag">内衣</div>
            <div class="category-tag">泳装</div>
            <div class="category-tag">古风</div>
            <div class="category-tag">欧美</div>
        </div>

        <!-- 推荐模特 -->
        <div class="px-4 mb-6">
            <h2 class="text-lg font-bold mb-4 flex items-center">
                <i class="fas fa-star text-yellow-400 mr-2"></i>
                推荐模特
            </h2>
            <div class="bg-gradient-to-r from-pink-800 to-purple-800 rounded-2xl p-4 flex items-center space-x-4">
                <div class="w-16 h-16 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=100&h=100&fit=crop&crop=face" alt="推荐模特" class="w-full h-full object-cover">
                </div>
                <div class="flex-1">
                    <h3 class="font-bold text-white">艾米丽·克拉克</h3>
                    <p class="text-purple-200 text-sm">国际超模 · 粉丝 128万</p>
                    <div class="flex items-center mt-1">
                        <div class="flex text-yellow-400 text-xs">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <span class="text-purple-200 text-xs ml-2">5.0</span>
                    </div>
                </div>
                <button class="bg-gradient-to-r from-pink-500 to-purple-600 px-4 py-2 rounded-full text-sm font-medium">
                    关注
                </button>
            </div>
        </div>

        <!-- 模特列表 -->
        <div class="px-4">
            <div class="grid grid-cols-2 gap-4">
                <!-- 模特卡片1 -->
                <div class="model-card" onclick="window.parent.showPage('model-detail')">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=200&h=250&fit=crop&crop=face" alt="模特" class="w-full h-48 object-cover rounded-t-xl">
                        <div class="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                            HOT
                        </div>
                        <div class="absolute bottom-2 left-2 bg-black/50 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full">
                            <i class="fas fa-heart text-red-400 mr-1"></i>
                            12.5K
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-bold text-white text-base mb-1">莉莉·詹姆斯</h3>
                        <p class="text-purple-300 text-sm mb-2">时装模特 · 欧美风格</p>
                        <p class="text-gray-400 text-xs mb-3">往期作品共 <span class="text-pink-400 font-semibold">156期</span></p>
                        <div class="flex items-center justify-between">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                            </div>
                            <span class="text-gray-400 text-xs">4.8</span>
                        </div>
                    </div>
                </div>

                <!-- 模特卡片2 -->
                <div class="model-card">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1488716820095-cbe80883c496?w=200&h=250&fit=crop&crop=face" alt="模特" class="w-full h-48 object-cover rounded-t-xl">
                        <div class="absolute top-2 right-2 bg-purple-500 text-white text-xs px-2 py-1 rounded-full">
                            NEW
                        </div>
                        <div class="absolute bottom-2 left-2 bg-black/50 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full">
                            <i class="fas fa-heart text-red-400 mr-1"></i>
                            8.9K
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-bold text-white text-base mb-1">索菲亚·维加</h3>
                        <p class="text-purple-300 text-sm mb-2">内衣模特 · 性感魅力</p>
                        <p class="text-gray-400 text-xs mb-3">往期作品共 <span class="text-pink-400 font-semibold">89期</span></p>
                        <div class="flex items-center justify-between">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="text-gray-400 text-xs">5.0</span>
                        </div>
                    </div>
                </div>

                <!-- 模特卡片3 -->
                <div class="model-card">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=200&h=250&fit=crop&crop=face" alt="模特" class="w-full h-48 object-cover rounded-t-xl">
                        <div class="absolute bottom-2 left-2 bg-black/50 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full">
                            <i class="fas fa-heart text-red-400 mr-1"></i>
                            15.2K
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-bold text-white text-base mb-1">艾娃·陈</h3>
                        <p class="text-purple-300 text-sm mb-2">古风模特 · 东方韵味</p>
                        <p class="text-gray-400 text-xs mb-3">往期作品共 <span class="text-pink-400 font-semibold">203期</span></p>
                        <div class="flex items-center justify-between">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="text-gray-400 text-xs">4.9</span>
                        </div>
                    </div>
                </div>

                <!-- 模特卡片4 -->
                <div class="model-card">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?w=200&h=250&fit=crop&crop=face" alt="模特" class="w-full h-48 object-cover rounded-t-xl">
                        <div class="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                            VIP
                        </div>
                        <div class="absolute bottom-2 left-2 bg-black/50 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full">
                            <i class="fas fa-heart text-red-400 mr-1"></i>
                            22.1K
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-bold text-white text-base mb-1">娜塔莉·波特曼</h3>
                        <p class="text-purple-300 text-sm mb-2">欧美模特 · 高端时尚</p>
                        <p class="text-gray-400 text-xs mb-3">往期作品共 <span class="text-pink-400 font-semibold">312期</span></p>
                        <div class="flex items-center justify-between">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="text-gray-400 text-xs">5.0</span>
                        </div>
                    </div>
                </div>

                <!-- 模特卡片5 -->
                <div class="model-card">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1506863530036-1efeddceb993?w=200&h=250&fit=crop&crop=face" alt="模特" class="w-full h-48 object-cover rounded-t-xl">
                        <div class="absolute bottom-2 left-2 bg-black/50 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full">
                            <i class="fas fa-heart text-red-400 mr-1"></i>
                            9.8K
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-bold text-white text-base mb-1">安娜·贝拉</h3>
                        <p class="text-purple-300 text-sm mb-2">泳装模特 · 活力青春</p>
                        <p class="text-gray-400 text-xs mb-3">往期作品共 <span class="text-pink-400 font-semibold">67期</span></p>
                        <div class="flex items-center justify-between">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <span class="text-gray-400 text-xs">4.2</span>
                        </div>
                    </div>
                </div>

                <!-- 模特卡片6 -->
                <div class="model-card">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1517841905240-472988babdf9?w=200&h=250&fit=crop&crop=face" alt="模特" class="w-full h-48 object-cover rounded-t-xl">
                        <div class="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                            推荐
                        </div>
                        <div class="absolute bottom-2 left-2 bg-black/50 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full">
                            <i class="fas fa-heart text-red-400 mr-1"></i>
                            18.3K
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-bold text-white text-base mb-1">玛丽亚·桑切斯</h3>
                        <p class="text-purple-300 text-sm mb-2">时装模特 · 拉丁风情</p>
                        <p class="text-gray-400 text-xs mb-3">往期作品共 <span class="text-pink-400 font-semibold">145期</span></p>
                        <div class="flex items-center justify-between">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                            </div>
                            <span class="text-gray-400 text-xs">4.7</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bottom-nav">
        <div class="nav-item">
            <span>首页</span>
        </div>
        <div class="nav-item">
            <span>热榜</span>
        </div>
        <div class="nav-item">
            <span>社区</span>
        </div>
        <div class="nav-item active">
            <span>模特</span>
        </div>
        <div class="nav-item">
            <span>我的</span>
        </div>
    </div>
</body>
</html>