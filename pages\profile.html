<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的 - 时尚先锋摄影</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../css/style.css" rel="stylesheet">
    <style>
        /* 确保页面样式正确加载 */
        .menu-item {
            color: white !important;
        }
        .menu-item span {
            color: white !important;
        }
        .menu-item i {
            opacity: 1 !important;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 text-white min-h-screen">
    <!-- 状态栏 -->
    <div class="status-bar">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 个人资料头部 -->
        <div class="relative">
            <!-- 背景图片 -->
            <div class="h-48 bg-gradient-to-br from-purple-600 via-pink-600 to-blue-600 relative overflow-hidden">
                <div class="absolute inset-0 bg-black/20"></div>
                <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
            </div>
            
            <!-- 用户信息卡片 -->
            <div class="absolute -bottom-16 left-4 right-4">
                <div class="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 shadow-2xl">
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <div class="w-20 h-20 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center border-4 border-white/20">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=80&h=80&fit=crop&crop=face" alt="头像" class="w-full h-full rounded-full object-cover">
                            </div>
                            <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                                <i class="fas fa-check text-xs text-white"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h2 class="text-xl font-bold text-white mb-1">时尚达人 Emily</h2>
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-xs px-2 py-1 rounded-full font-bold">VIP</span>
                                <span class="text-purple-200 text-sm">等级 Lv.5</span>
                            </div>
                            <div class="flex items-center">
                                <div class="bg-white/20 rounded-full h-2 flex-1 mr-2">
                                    <div class="bg-gradient-to-r from-yellow-400 to-orange-500 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                                <span class="text-xs text-purple-200">75%</span>
                            </div>
                        </div>
                        <button class="bg-gradient-to-r from-pink-500 to-purple-600 px-4 py-2 rounded-full text-sm font-medium">
                            编辑
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 间距 -->
        <div class="h-20"></div>

        <!-- 统计数据 -->
        <div class="grid grid-cols-4 gap-3 mx-4 mb-6">
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/10">
                <div class="text-2xl font-bold text-pink-400 mb-1">128</div>
                <div class="text-xs text-gray-300">收藏</div>
            </div>
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/10">
                <div class="text-2xl font-bold text-purple-400 mb-1">56</div>
                <div class="text-xs text-gray-300">关注</div>
            </div>
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/10">
                <div class="text-2xl font-bold text-blue-400 mb-1">89</div>
                <div class="text-xs text-gray-300">粉丝</div>
            </div>
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/10">
                <div class="text-2xl font-bold text-green-400 mb-1">23</div>
                <div class="text-xs text-gray-300">作品</div>
            </div>
        </div>

        <!-- 快捷功能 -->
        <div class="mx-4 mb-6">
            <div class="grid grid-cols-4 gap-3">
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/10">
                    <i class="fas fa-wallet text-2xl text-green-400 mb-2"></i>
                    <div class="text-xs text-gray-300">钱包</div>
                    <div class="text-sm font-bold text-white mt-1">¥128.50</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/10">
                    <i class="fas fa-gift text-2xl text-pink-400 mb-2"></i>
                    <div class="text-xs text-gray-300">积分</div>
                    <div class="text-sm font-bold text-white mt-1">2,580</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/10">
                    <i class="fas fa-crown text-2xl text-yellow-400 mb-2"></i>
                    <div class="text-xs text-gray-300">VIP</div>
                    <div class="text-xs text-yellow-300 mt-1">365天</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/10">
                    <i class="fas fa-star text-2xl text-orange-400 mb-2"></i>
                    <div class="text-xs text-gray-300">等级</div>
                    <div class="text-sm font-bold text-white mt-1">Lv.5</div>
                </div>
            </div>
        </div>

        <!-- VIP特权卡片 -->
        <div class="mx-4 mb-6">
            <div class="bg-gradient-to-r from-yellow-600 to-orange-600 rounded-2xl p-5 relative overflow-hidden">
                <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
                <div class="relative z-10">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-crown text-3xl text-yellow-200"></i>
                            <div>
                                <div class="font-bold text-white text-lg">VIP会员特权</div>
                                <div class="text-sm text-yellow-200">专享特权，畅享无限精彩</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-yellow-200"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能菜单 -->
        <div class="mx-4 space-y-4">
            <!-- 我的内容 -->
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/10">
                <div class="p-4 border-b border-white/10">
                    <h3 class="font-bold text-white text-lg">我的内容</h3>
                </div>
                <div class="menu-item">
                    <i class="fas fa-heart text-pink-400"></i>
                    <span>我的收藏</span>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-400">128</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
                <div class="menu-item border-t border-white/10">
                    <i class="fas fa-download text-blue-400"></i>
                    <span>下载管理</span>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-400">23</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
                <div class="menu-item border-t border-white/10">
                    <i class="fas fa-history text-green-400"></i>
                    <span>浏览历史</span>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="menu-item border-t border-white/10">
                    <i class="fas fa-comment-dots text-purple-400"></i>
                    <span>我的评论</span>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>

            <!-- 设置与帮助 -->
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/10">
                <div class="p-4 border-b border-white/10">
                    <h3 class="font-bold text-white text-lg">设置与帮助</h3>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog text-gray-400"></i>
                    <span>设置</span>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="menu-item border-t border-white/10">
                    <i class="fas fa-shield-alt text-blue-400"></i>
                    <span>隐私安全</span>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="menu-item border-t border-white/10">
                    <i class="fas fa-question-circle text-green-400"></i>
                    <span>帮助与反馈</span>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="menu-item border-t border-white/10">
                    <i class="fas fa-info-circle text-purple-400"></i>
                    <span>关于我们</span>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- 底部间距 -->
        <div class="h-6"></div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bottom-nav">
        <div class="nav-item">
            <span>首页</span>
        </div>
        <div class="nav-item">
            <span>热榜</span>
        </div>
        <div class="nav-item">
            <span>社区</span>
        </div>
        <div class="nav-item">
            <span>模特</span>
        </div>
        <div class="nav-item active">
            <span>我的</span>
        </div>
    </div>
</body>
</html>