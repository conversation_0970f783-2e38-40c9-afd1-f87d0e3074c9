// This file still here for a legacy code and will be removed in a near time
require('./es6.object.create');
require('./es6.object.define-property');
require('./es6.object.define-properties');
require('./es6.object.get-own-property-descriptor');
require('./es6.object.get-prototype-of');
require('./es6.object.keys');
require('./es6.object.get-own-property-names');
require('./es6.object.freeze');
require('./es6.object.seal');
require('./es6.object.prevent-extensions');
require('./es6.object.is-frozen');
require('./es6.object.is-sealed');
require('./es6.object.is-extensible');
require('./es6.function.bind');
require('./es6.array.is-array');
require('./es6.array.join');
require('./es6.array.slice');
require('./es6.array.sort');
require('./es6.array.for-each');
require('./es6.array.map');
require('./es6.array.filter');
require('./es6.array.some');
require('./es6.array.every');
require('./es6.array.reduce');
require('./es6.array.reduce-right');
require('./es6.array.index-of');
require('./es6.array.last-index-of');
require('./es6.date.now');
require('./es6.date.to-iso-string');
require('./es6.date.to-json');
require('./es6.parse-int');
require('./es6.parse-float');
require('./es6.string.trim');
require('./es6.regexp.to-string');
