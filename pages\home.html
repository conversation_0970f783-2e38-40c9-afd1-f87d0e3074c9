<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <div class="app-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>9:41</span>
            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
        </div>
        
        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">JANS</div>
            <div class="user-status">
                <button onclick="window.location.href='login.html'" class="text-sm bg-white bg-opacity-20 px-3 py-1 rounded-full">
                    登录
                </button>
            </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content">
            <!-- Banner轮播 -->
            <div class="relative mb-6 rounded-2xl overflow-hidden">
                <div class="h-48 bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center">
                    <div class="text-center">
                        <h2 class="text-3xl font-bold text-white mb-2">时尚先锋</h2>
                        <p class="text-white text-opacity-90">探索时尚摄影的无限可能</p>
                    </div>
                </div>
                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    <div class="w-2 h-2 bg-white rounded-full"></div>
                    <div class="w-2 h-2 bg-white bg-opacity-50 rounded-full"></div>
                    <div class="w-2 h-2 bg-white bg-opacity-50 rounded-full"></div>
                </div>
            </div>
            
            <!-- 功能按钮区 -->
            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="col-span-1 space-y-4">
                    <button onclick="window.location.href='vip.html'" class="w-full h-20 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex flex-col items-center justify-center text-white font-semibold shadow-lg">
                        <i class="fas fa-crown text-xl mb-1"></i>
                        <span class="text-sm">VIP会员</span>
                    </button>
                    <button onclick="window.location.href='community.html'" class="w-full h-20 bg-gradient-to-br from-green-400 to-blue-500 rounded-xl flex flex-col items-center justify-center text-white font-semibold shadow-lg">
                        <i class="fas fa-comments text-xl mb-1"></i>
                        <span class="text-sm">社区吐槽</span>
                    </button>
                </div>
                <div class="col-span-2">
                    <button onclick="window.location.href='recharge.html'" class="w-full h-44 bg-gradient-to-br from-pink-500 to-purple-600 rounded-xl flex flex-col items-center justify-center text-white font-semibold shadow-lg">
                        <i class="fas fa-wallet text-3xl mb-2"></i>
                        <span class="text-lg">充值</span>
                        <span class="text-sm opacity-80">快速充值享受更多服务</span>
                    </button>
                </div>
            </div>
            
            <!-- 电子杂志区域 -->
            <div class="mb-6">
                <h3 class="text-xl font-bold text-white mb-4 flex items-center">
                    <i class="fas fa-book-open mr-2 text-purple-400"></i>
                    精选杂志
                </h3>
                <div class="grid grid-cols-3 gap-4">
                    <div onclick="window.location.href='magazine-detail.html'" class="cursor-pointer">
                        <div class="bg-gradient-to-br from-purple-600 to-pink-600 h-32 rounded-lg mb-2 flex items-center justify-center">
                            <i class="fas fa-camera text-white text-2xl"></i>
                        </div>
                        <p class="text-white text-sm text-center">时尚前沿</p>
                    </div>
                    <div onclick="window.location.href='magazine-detail.html'" class="cursor-pointer">
                        <div class="bg-gradient-to-br from-blue-600 to-purple-600 h-32 rounded-lg mb-2 flex items-center justify-center">
                            <i class="fas fa-star text-white text-2xl"></i>
                        </div>
                        <p class="text-white text-sm text-center">明星写真</p>
                    </div>
                    <div onclick="window.location.href='magazine-detail.html'" class="cursor-pointer">
                        <div class="bg-gradient-to-br from-green-600 to-blue-600 h-32 rounded-lg mb-2 flex items-center justify-center">
                            <i class="fas fa-heart text-white text-2xl"></i>
                        </div>
                        <p class="text-white text-sm text-center">情感大片</p>
                    </div>
                    <div onclick="window.location.href='magazine-detail.html'" class="cursor-pointer">
                        <div class="bg-gradient-to-br from-red-600 to-pink-600 h-32 rounded-lg mb-2 flex items-center justify-center">
                            <i class="fas fa-fire text-white text-2xl"></i>
                        </div>
                        <p class="text-white text-sm text-center">热门推荐</p>
                    </div>
                    <div onclick="window.location.href='magazine-detail.html'" class="cursor-pointer">
                        <div class="bg-gradient-to-br from-yellow-600 to-red-600 h-32 rounded-lg mb-2 flex items-center justify-center">
                            <i class="fas fa-gem text-white text-2xl"></i>
                        </div>
                        <p class="text-white text-sm text-center">奢华时尚</p>
                    </div>
                    <div onclick="window.location.href='magazine-detail.html'" class="cursor-pointer">
                        <div class="bg-gradient-to-br from-indigo-600 to-purple-600 h-32 rounded-lg mb-2 flex items-center justify-center">
                            <i class="fas fa-magic text-white text-2xl"></i>
                        </div>
                        <p class="text-white text-sm text-center">艺术摄影</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 客服浮动按钮 -->
        <div class="floating-btn">
            <i class="fas fa-headset"></i>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="home.html" class="nav-item active">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </a>
            <a href="models.html" class="nav-item">
                <i class="fas fa-user-friends"></i>
                <span>模特</span>
            </a>
            <a href="ranking.html" class="nav-item">
                <i class="fas fa-trophy"></i>
                <span>热榜</span>
            </a>
            <a href="profile.html" class="nav-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>
</body>
</html>