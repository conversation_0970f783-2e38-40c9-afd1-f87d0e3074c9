<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开屏页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <div class="app-container">
        <!-- 开屏大图 -->
        <div class="relative h-full flex flex-col justify-center items-center">
            <div class="absolute inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900"></div>
            <div class="absolute inset-0 bg-black bg-opacity-40"></div>
            
            <!-- Logo区域 -->
            <div class="relative z-10 text-center mb-8">
                <h1 class="text-6xl font-bold text-white mb-4 tracking-wider" style="text-shadow: 0 0 20px rgba(255,255,255,0.5);">JANS</h1>
                <p class="text-2xl text-gray-200 font-light">时尚先锋摄影</p>
                <div class="w-32 h-1 bg-gradient-to-r from-pink-500 to-purple-500 mx-auto mt-4 rounded-full"></div>
            </div>
            
            <!-- 最近公告 -->
            <div class="relative z-10 bg-black bg-opacity-60 backdrop-blur-lg rounded-2xl p-6 mx-6 border border-white border-opacity-20">
                <div class="flex items-center mb-3">
                    <i class="fas fa-bullhorn text-yellow-400 mr-2"></i>
                    <h3 class="text-lg font-semibold text-white">最新公告</h3>
                </div>
                <p class="text-gray-300 text-sm leading-relaxed">
                    欢迎来到时尚先锋摄影！本月新增超级VIP会员服务，享受独家高清内容和无限下载特权。立即升级体验更多精彩内容！
                </p>
                <div class="mt-4 text-right">
                    <span class="text-xs text-gray-400">2024-01-15</span>
                </div>
            </div>
            
            <!-- 进入按钮 -->
            <div class="relative z-10 mt-8">
                <button onclick="window.parent.location.href='pages/home.html'" class="bg-gradient-to-r from-pink-500 to-purple-600 px-8 py-3 rounded-full text-white font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    <i class="fas fa-arrow-right mr-2"></i>
                    进入应用
                </button>
            </div>
            
            <!-- 装饰元素 -->
            <div class="absolute top-20 left-10 w-20 h-20 bg-pink-500 bg-opacity-20 rounded-full blur-xl"></div>
            <div class="absolute bottom-40 right-10 w-32 h-32 bg-purple-500 bg-opacity-20 rounded-full blur-xl"></div>
        </div>
    </div>
</body>
</html>